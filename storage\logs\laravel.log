[2025-06-02 10:37:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:07:52 +0630","user_id":1} 
[2025-06-02 10:38:26] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"14527864785","label":"list"}]} 
[2025-06-02 10:40:12] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:10:11 +0630","user_id":1} 
[2025-06-02 10:40:33] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"14587458525","label":"test"}]} 
[2025-06-02 10:41:07] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:11:07 +0630","user_id":1} 
[2025-06-02 10:41:29] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"14523652874","label":"new test"}]} 
[2025-06-02 10:42:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:12:54 +0630","user_id":1} 
[2025-06-02 10:43:11] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"25463125741","label":"qwerty"}]} 
[2025-06-02 10:43:27] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"},{"id":26,"numbers":"25463125741","label":"qwerty"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:45:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:15:10 +0630","user_id":1} 
[2025-06-02 10:47:23] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:17:23 +0630","user_id":1} 
[2025-06-02 10:47:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:17:37 +0630","user_id":1} 
[2025-06-02 10:47:41] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:48:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:18:10 +0630","user_id":1} 
[2025-06-02 10:49:06] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:19:05 +0630","user_id":1} 
[2025-06-02 10:49:54] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:50:31] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"13151546358","label":""}]} 
[2025-06-02 10:50:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:20:37 +0630","user_id":1} 
[2025-06-02 10:50:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:20:55 +0630","user_id":1} 
[2025-06-02 10:51:04] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:21:03 +0630","user_id":1} 
[2025-06-02 10:51:27] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"14523652875","label":""}]} 
[2025-06-02 10:51:33] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:51:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:21:55 +0630","user_id":1} 
[2025-06-02 10:52:47] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:22:46 +0630","user_id":1} 
[2025-06-02 10:52:50] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:53:03] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:23:02 +0630","user_id":1} 
[2025-06-02 10:53:05] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:53:15] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:23:14 +0630","user_id":1} 
[2025-06-02 10:54:58] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:24:57 +0630","user_id":1} 
[2025-06-02 10:55:02] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:55:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:25:31 +0630","user_id":1} 
[2025-06-02 10:55:34] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 10:56:54] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:26:54 +0630","user_id":1} 
[2025-06-02 10:57:36] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:27:36 +0630","user_id":1} 
[2025-06-02 10:58:12] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:28:11 +0630","user_id":1} 
[2025-06-02 10:58:58] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:28:57 +0630","user_id":1} 
[2025-06-02 10:59:00] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 11:01:04] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:31:04 +0630","user_id":1} 
[2025-06-02 11:01:09] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 11:01:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:31:31 +0630","user_id":1} 
[2025-06-02 11:02:15] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:32:14 +0630","user_id":1} 
[2025-06-02 11:03:00] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:33:00 +0630","user_id":1} 
[2025-06-02 11:03:06] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 11:04:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:34:15 +0630","user_id":1} 
[2025-06-02 11:04:19] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 11:05:20] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:35:19 +0630","user_id":1} 
[2025-06-02 11:05:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:35:56 +0630","user_id":1} 
[2025-06-02 11:08:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:38:37 +0630","user_id":1} 
[2025-06-02 11:09:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:39:53 +0630","user_id":1} 
[2025-06-02 11:10:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:40:52 +0630","user_id":1} 
[2025-06-02 11:11:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:41:36 +0630","user_id":1} 
[2025-06-02 11:12:21] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:42:20 +0630","user_id":1} 
[2025-06-02 11:12:50] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:42:49 +0630","user_id":1} 
[2025-06-02 11:13:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:43:15 +0630","user_id":1} 
[2025-06-02 11:13:34] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"87451256352","label":"qwerty"}]} 
[2025-06-02 11:20:47] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:50:46 +0630","user_id":1} 
[2025-06-02 11:22:22] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:52:21 +0630","user_id":1} 
[2025-06-02 11:23:32] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"},{"id":27,"numbers":"87451256352","label":"qwerty"}],"newFaxInputs":[{"number":"74527684527","label":"sdfd"}]} 
[2025-06-02 11:27:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 16:57:55 +0630","user_id":1} 
[2025-06-02 11:28:46] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test new"}],"newFaxInputs":[{"number":"87542785784","label":"new"}]} 
[2025-06-02 11:33:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:03:24 +0630","user_id":1} 
[2025-06-02 11:33:47] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:03:46 +0630","user_id":1} 
[2025-06-02 11:34:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:04:36 +0630","user_id":1} 
[2025-06-02 11:37:07] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:07:07 +0630","user_id":1} 
[2025-06-02 11:40:51] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:10:50 +0630","user_id":1} 
[2025-06-02 11:41:00] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:41:00] local.INFO: Duplicate found within form data  
[2025-06-02 11:41:34] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:11:34 +0630","user_id":1} 
[2025-06-02 11:42:07] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:12:06 +0630","user_id":1} 
[2025-06-02 11:42:15] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:42:15] local.INFO: Duplicate found within form data  
[2025-06-02 11:42:17] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:42:17] local.INFO: Duplicate found within form data  
[2025-06-02 11:43:23] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:13:22 +0630","user_id":1} 
[2025-06-02 11:43:29] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:43:29] local.INFO: Duplicate found within form data  
[2025-06-02 11:43:45] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:13:44 +0630","user_id":1} 
[2025-06-02 11:43:58] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:43:58] local.INFO: Duplicate found within form data  
[2025-06-02 11:44:29] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:14:28 +0630","user_id":1} 
[2025-06-02 11:44:33] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:44:33] local.INFO: Duplicate found within form data  
[2025-06-02 11:44:47] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:44:47] local.INFO: Duplicate found within form data  
[2025-06-02 11:44:49] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:44:49] local.INFO: Duplicate found within form data  
[2025-06-02 11:44:50] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:44:50] local.INFO: Duplicate found within form data  
[2025-06-02 11:44:51] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528","18885110528"],"unique":["18885110528"],"count_all":2,"count_unique":1} 
[2025-06-02 11:44:51] local.INFO: Duplicate found within form data  
[2025-06-02 11:45:03] local.INFO: Duplication check - All numbers collected: {"allNumbers":["18885110528"],"unique":["18885110528"],"count_all":1,"count_unique":1} 
[2025-06-02 11:45:03] local.INFO: Checking existing fax number against database: {"index":0,"number":"18885110528","id":16,"exists":false} 
[2025-06-02 11:45:28] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:15:28 +0630","user_id":1} 
[2025-06-02 11:46:38] local.ERROR: Undefined variable $message {"view":{"view":"C:\\KodeCreators\\newlife-panel\\resources\\views\\livewire\\settings\\fax-options.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1897391552 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#586</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1897391552\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-1181198962 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"11 characters\">Fax Options</span>\"
</pre><script>Sfdump(\"sf-dump-1181198962\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $message at C:\\KodeCreators\\newlife-panel\\resources\\views\\livewire\\settings\\fax-options.blade.php:14)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(83): include('C:\\\\KodeCreators...')
#1 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Settings\\FaxOptions->Livewire\\ComponentConcerns\\{closure}()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Response))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(113): Livewire\\LifecycleManager->initialDehydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Livewire\\LivewireManager->mount('settings.fax-op...', Array)
#11 C:\\KodeCreators\\newlife-panel\\resources\\views\\settings\\fax-options.blade.php(3): Illuminate\\Support\\Facades\\Facade::__callStatic('mount', Array)
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#15 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#17 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#72 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#73 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#74 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $message at C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\b4e038137ac669222c8fcf5106e456bf.php:14)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 14)
#1 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\b4e038137ac669222c8fcf5106e456bf.php(14): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 14)
#2 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(83): include('C:\\\\KodeCreators...')
#3 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Settings\\FaxOptions->Livewire\\ComponentConcerns\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Response))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(113): Livewire\\LifecycleManager->initialDehydrate()
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Livewire\\LivewireManager->mount('settings.fax-op...', Array)
#13 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\4c5d02dddc567ffb1e82b8ff009dacb7.php(10): Illuminate\\Support\\Facades\\Facade::__callStatic('mount', Array)
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#17 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#19 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#73 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#74 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#75 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#76 {main}
"} 
[2025-06-02 11:46:50] local.ERROR: syntax error, unexpected token "endif", expecting end of file {"view":{"view":"C:\\KodeCreators\\newlife-panel\\resources\\views\\livewire\\settings\\fax-options.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#586</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-56366042 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"11 characters\">Fax Options</span>\"
</pre><script>Sfdump(\"sf-dump-56366042\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): syntax error, unexpected token \"endif\", expecting end of file at C:\\KodeCreators\\newlife-panel\\resources\\views\\livewire\\settings\\fax-options.blade.php:17)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#1 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#62 {main}

[previous exception] [object] (ParseError(code: 0): syntax error, unexpected token \"endif\", expecting end of file at C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\b4e038137ac669222c8fcf5106e456bf.php:18)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Settings\\FaxOptions->Livewire\\ComponentConcerns\\{closure}()
#1 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#5 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Response))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(113): Livewire\\LifecycleManager->initialDehydrate()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Livewire\\LivewireManager->mount('settings.fax-op...', Array)
#10 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\4c5d02dddc567ffb1e82b8ff009dacb7.php(10): Illuminate\\Support\\Facades\\Facade::__callStatic('mount', Array)
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#14 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#16 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#71 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#72 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#73 {main}
"} 
[2025-06-02 11:47:03] local.ERROR: Undefined variable $message {"view":{"view":"C:\\KodeCreators\\newlife-panel\\resources\\views\\livewire\\settings\\fax-options.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1286605506 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#586</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1286605506\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","page_title":"<pre class=sf-dump id=sf-dump-288875075 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"11 characters\">Fax Options</span>\"
</pre><script>Sfdump(\"sf-dump-288875075\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $message at C:\\KodeCreators\\newlife-panel\\resources\\views\\livewire\\settings\\fax-options.blade.php:14)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(83): include('C:\\\\KodeCreators...')
#1 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Settings\\FaxOptions->Livewire\\ComponentConcerns\\{closure}()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Response))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(113): Livewire\\LifecycleManager->initialDehydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Livewire\\LivewireManager->mount('settings.fax-op...', Array)
#11 C:\\KodeCreators\\newlife-panel\\resources\\views\\settings\\fax-options.blade.php(3): Illuminate\\Support\\Facades\\Facade::__callStatic('mount', Array)
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#15 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#17 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#72 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#73 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#74 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $message at C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\b4e038137ac669222c8fcf5106e456bf.php:14)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 14)
#1 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\b4e038137ac669222c8fcf5106e456bf.php(14): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\KodeCreators...', 14)
#2 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(83): include('C:\\\\KodeCreators...')
#3 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Settings\\FaxOptions->Livewire\\ComponentConcerns\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#6 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#7 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Response))
#11 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LivewireManager.php(113): Livewire\\LifecycleManager->initialDehydrate()
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Livewire\\LivewireManager->mount('settings.fax-op...', Array)
#13 C:\\KodeCreators\\newlife-panel\\storage\\framework\\views\\4c5d02dddc567ffb1e82b8ff009dacb7.php(10): Illuminate\\Support\\Facades\\Facade::__callStatic('mount', Array)
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\KodeCreators...')
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\KodeCreators...', Array)
#17 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\KodeCreators...', Array)
#19 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\KodeCreators...', Array)
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\KodeCreators...', Array)
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#73 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#74 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#75 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#76 {main}
"} 
[2025-06-02 11:47:31] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:17:30 +0630","user_id":1} 
[2025-06-02 11:58:12] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:28:11 +0630","user_id":1} 
[2025-06-02 11:58:30] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:28:29 +0630","user_id":1} 
[2025-06-02 11:58:32] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-02 11:58:34] local.INFO: Staff bulk import completed {"import_id":117,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-02 11:58:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:28:37 +0630","user_id":1} 
[2025-06-02 11:59:27] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:29:26 +0630","user_id":1} 
[2025-06-02 11:59:59] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:29:58 +0630","user_id":1} 
[2025-06-02 12:00:03] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:30:02 +0630","user_id":1} 
[2025-06-02 12:00:38] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:30:38 +0630","user_id":1} 
[2025-06-02 12:01:05] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:31:04 +0630","user_id":3} 
[2025-06-02 12:01:09] local.INFO: Blank row detected {"row_number":2,"consecutive_blank_rows":1} 
[2025-06-02 12:01:09] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":1} 
[2025-06-02 12:01:09] local.INFO: Blank row detected {"row_number":3,"consecutive_blank_rows":2} 
[2025-06-02 12:01:09] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":2} 
[2025-06-02 12:01:09] local.INFO: Blank row detected {"row_number":4,"consecutive_blank_rows":3} 
[2025-06-02 12:01:09] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":3} 
[2025-06-02 12:01:09] local.INFO: Blank row detected {"row_number":5,"consecutive_blank_rows":4} 
[2025-06-02 12:01:09] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":4} 
[2025-06-02 12:01:09] local.INFO: Blank row detected {"row_number":6,"consecutive_blank_rows":5} 
[2025-06-02 12:01:09] local.INFO: 5 or more consecutive blank rows detected - stopping processing and discarding blank rows {"row_number":6,"consecutive_blank_rows":5} 
[2025-06-02 12:01:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:31:10 +0630","user_id":3} 
[2025-06-02 12:01:42] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:31:41 +0630","user_id":1} 
[2025-06-02 12:02:03] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:32:02 +0630","user_id":1} 
[2025-06-02 12:02:40] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-02 12:02:41] local.INFO: Staff bulk import completed {"import_id":118,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-02 12:02:44] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:32:43 +0630","user_id":1} 
[2025-06-02 12:03:04] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:33:03 +0630","user_id":1} 
[2025-06-02 12:03:07] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:33:06 +0630","user_id":1} 
[2025-06-02 12:03:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:33:10 +0630","user_id":1} 
[2025-06-02 12:03:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:33:15 +0630","user_id":1} 
[2025-06-02 12:03:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:33:55 +0630","user_id":1} 
[2025-06-02 12:04:14] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-02 12:04:15] local.INFO: Staff bulk import completed {"import_id":119,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-02 12:04:18] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:34:17 +0630","user_id":1} 
[2025-06-02 12:04:35] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:34:34 +0630","user_id":3} 
[2025-06-02 12:04:58] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:34:57 +0630","user_id":1} 
[2025-06-02 12:05:16] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png","exists":false,"full_path":"C:\\KodeCreators\\newlife-panel\\storage\\app/public/signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png"} 
[2025-06-02 12:05:19] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png","exists":false,"full_path":"C:\\KodeCreators\\newlife-panel\\storage\\app/public/signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png"} 
[2025-06-02 12:05:22] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:35:21 +0630","user_id":1} 
[2025-06-02 12:05:26] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:35:25 +0630","user_id":1} 
[2025-06-02 12:06:14] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png","exists":false,"full_path":"C:\\KodeCreators\\newlife-panel\\storage\\app/public/signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png"} 
[2025-06-02 12:06:17] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:36:16 +0630","user_id":1} 
[2025-06-02 12:06:21] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:36:20 +0630","user_id":1} 
[2025-06-02 12:06:23] local.INFO: Filtering by provider {"provider_id":"3"} 
[2025-06-02 12:06:27] local.INFO: Filtering by provider {"provider_id":"4"} 
[2025-06-02 12:06:42] local.INFO: Downloaded PDFs {"total_files":9,"signed_and_sent_for_approval_files":0,"added_to_zip":9} 
[2025-06-02 12:06:58] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-06-02 17:36:57 +0630","error":"Trailing data"} 
[2025-06-02 12:06:58] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-02 17:34:34 +0630"} 
[2025-06-02 12:06:58] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":727,"signed_at_db":"2025-06-02 17:34:34","formatted_signed_at":"06/02/2025 05:34 PM"} 
[2025-06-02 12:06:59] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"sahil panchal","count":1} 
[2025-06-02 12:07:02] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:37:01 +0630","user_id":3} 
[2025-06-02 12:07:05] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:37:04 +0630","user_id":3} 
[2025-06-02 12:07:17] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:37:16 +0630","user_id":1} 
[2025-06-02 12:07:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:37:31 +0630","user_id":1} 
[2025-06-02 12:07:35] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:37:34 +0630","user_id":1} 
[2025-06-02 12:07:49] local.INFO: Fax sent successfully {"to":["+18885110528"],"from":"+18669938841","file":"/storage/transient-6c8bafa7b84c43c99e0cc9e77013e536.pdf"} 
[2025-06-02 12:07:52] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:37:51 +0630","user_id":1} 
[2025-06-02 12:07:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:37:54 +0630","user_id":1} 
[2025-06-02 12:08:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:38:15 +0630","user_id":3} 
[2025-06-02 12:08:21] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-06-02 17:38:20 +0630","error":"Trailing data"} 
[2025-06-02 12:08:21] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-02 17:38:15 +0630"} 
[2025-06-02 12:08:21] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":728,"signed_at_db":"2025-06-02 17:38:15","formatted_signed_at":"06/02/2025 05:38 PM"} 
[2025-06-02 12:08:21] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"sahil panchal","count":1} 
[2025-06-02 12:08:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:38:23 +0630","user_id":3} 
[2025-06-02 12:08:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:38:31 +0630","user_id":3} 
[2025-06-02 12:08:49] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:38:48 +0630","user_id":3} 
[2025-06-02 12:08:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:38:54 +0630","user_id":3} 
[2025-06-02 12:09:05] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-06-02 17:39:04 +0630","error":"Trailing data"} 
[2025-06-02 12:09:05] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-02 17:38:54 +0630"} 
[2025-06-02 12:09:05] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":729,"signed_at_db":"2025-06-02 17:38:54","formatted_signed_at":"06/02/2025 05:38 PM"} 
[2025-06-02 12:09:05] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"sahil panchal","count":1} 
[2025-06-02 12:09:09] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:07 +0630","user_id":3} 
[2025-06-02 12:09:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:10 +0630","user_id":3} 
[2025-06-02 12:09:28] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:28 +0630","user_id":3} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":6,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":7,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":8,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":9,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Processing row {"row_number":10,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:09:32] local.INFO: Error statistics {"total_rows":9,"error_rows":0,"valid_rows":9} 
[2025-06-02 12:09:35] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:34 +0630","user_id":3} 
[2025-06-02 12:09:36] local.INFO: Processing Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0} 
[2025-06-02 12:09:36] local.INFO: Excel processing completed {"processed_rows":9,"skipped_rows":0,"import_id":120} 
[2025-06-02 12:09:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:38 +0630","user_id":3} 
[2025-06-02 12:09:43] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:42 +0630","user_id":3} 
[2025-06-02 12:09:46] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-06-02 17:39:45 +0630","error":"Trailing data"} 
[2025-06-02 12:09:46] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-02 17:39:42 +0630"} 
[2025-06-02 12:09:46] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":736,"signed_at_db":"2025-06-02 17:39:42","formatted_signed_at":"06/02/2025 05:39 PM"} 
[2025-06-02 12:09:46] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"sahil panchal","count":1} 
[2025-06-02 12:09:49] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:48 +0630","user_id":3} 
[2025-06-02 12:09:51] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:50 +0630","user_id":3} 
[2025-06-02 12:09:59] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:39:58 +0630","user_id":3} 
[2025-06-02 12:10:01] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:40:01 +0630","user_id":3} 
[2025-06-02 12:10:01] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-06-02 17:40:01 +0630","error":"Trailing data"} 
[2025-06-02 12:10:01] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-02 17:40:01 +0630"} 
[2025-06-02 12:10:01] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":737,"signed_at_db":"2025-06-02 17:40:01","formatted_signed_at":"06/02/2025 05:40 PM"} 
[2025-06-02 12:10:02] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"sahil panchal","count":1} 
[2025-06-02 12:10:05] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:40:04 +0630","user_id":3} 
[2025-06-02 12:10:07] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:40:07 +0630","user_id":3} 
[2025-06-02 12:10:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:40:31 +0630","user_id":1} 
[2025-06-02 12:10:35] local.INFO: User signature path {"user_id":3,"signature_path":"signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png","exists":false,"full_path":"C:\\KodeCreators\\newlife-panel\\storage\\app/public/signatures/mWnXe9otdyaekGucQB58U0EFqLs5DjqJKqiQCB73.png"} 
[2025-06-02 12:10:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:40:38 +0630","user_id":1} 
[2025-06-02 12:10:44] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:40:43 +0630","user_id":1} 
[2025-06-02 12:13:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:43:55 +0630","user_id":1} 
[2025-06-02 12:14:45] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:44:44 +0630","user_id":1} 
[2025-06-02 12:14:57] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:44:56 +0630","user_id":1} 
[2025-06-02 12:16:12] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:46:12 +0630","user_id":1} 
[2025-06-02 12:19:29] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:49:28 +0630","user_id":1} 
[2025-06-02 12:19:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:49:54 +0630","user_id":1} 
[2025-06-02 12:19:58] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:49:57 +0630","user_id":1} 
[2025-06-02 12:20:47] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:50:46 +0630","user_id":1} 
[2025-06-02 12:22:30] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:52:29 +0630","user_id":1} 
[2025-06-02 12:23:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:53:10 +0630","user_id":3} 
[2025-06-02 12:23:15] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:53:14 +0630","user_id":3} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":6,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":7,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":8,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":9,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Processing row {"row_number":10,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-02 12:23:18] local.INFO: Error statistics {"total_rows":9,"error_rows":0,"valid_rows":9} 
[2025-06-02 12:23:20] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:53:19 +0630","user_id":3} 
[2025-06-02 12:23:21] local.INFO: Processing Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0} 
[2025-06-02 12:23:22] local.INFO: Excel processing completed {"processed_rows":9,"skipped_rows":0,"import_id":1} 
[2025-06-02 12:23:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:53:23 +0630","user_id":3} 
[2025-06-02 12:23:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:53:31 +0630","user_id":3} 
[2025-06-02 12:23:33] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-06-02 17:53:33 +0630","error":"Trailing data"} 
[2025-06-02 12:23:33] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-02 17:53:31 +0630"} 
[2025-06-02 12:23:33] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":5,"signed_at_db":"2025-06-02 17:53:31","formatted_signed_at":"06/02/2025 05:53 PM"} 
[2025-06-02 12:23:34] local.ERROR: The PHP GD extension is required, but is not installed. {"userId":3,"exception":"[object] (Exception(code: 0): The PHP GD extension is required, but is not installed. at C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\lib\\Cpdf.php:5813)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Adapter\\CPDF.php(669): Dompdf\\Cpdf->addPngFromFile('file://C:\\\\KodeC...', 42.265748031496, 280.9332519685, 82.727272727273, 60.0)
#1 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Renderer\\Image.php(65): Dompdf\\Adapter\\CPDF->image('file://C:\\\\KodeC...', 42.265748031496, 451.0667480315, 82.727272727273, 60.0, 'normal')
#2 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Renderer.php(289): Dompdf\\Renderer\\Image->render(Object(Dompdf\\FrameDecorator\\Image))
#3 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Renderer.php(128): Dompdf\\Renderer->_render_frame('image', Object(Dompdf\\FrameDecorator\\Image))
#4 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Renderer.php(195): Dompdf\\Renderer->render(Object(Dompdf\\FrameDecorator\\Image))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Renderer.php(195): Dompdf\\Renderer->render(Object(Dompdf\\FrameDecorator\\Block))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Renderer.php(195): Dompdf\\Renderer->render(Object(Dompdf\\FrameDecorator\\Block))
#7 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Renderer.php(195): Dompdf\\Renderer->render(Object(Dompdf\\FrameDecorator\\Block))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\FrameReflower\\Page.php(149): Dompdf\\Renderer->render(Object(Dompdf\\FrameDecorator\\Block))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\FrameDecorator\\AbstractFrameDecorator.php(913): Dompdf\\FrameReflower\\Page->reflow(NULL)
#10 C:\\KodeCreators\\newlife-panel\\vendor\\dompdf\\dompdf\\src\\Dompdf.php(765): Dompdf\\FrameDecorator\\AbstractFrameDecorator->reflow()
#11 C:\\KodeCreators\\newlife-panel\\vendor\\barryvdh\\laravel-dompdf\\src\\PDF.php(237): Dompdf\\Dompdf->render()
#12 C:\\KodeCreators\\newlife-panel\\vendor\\barryvdh\\laravel-dompdf\\src\\PDF.php(186): Barryvdh\\DomPDF\\PDF->render()
#13 C:\\KodeCreators\\newlife-panel\\app\\Http\\Controllers\\Admin\\ScriptController.php(1429): Barryvdh\\DomPDF\\PDF->output()
#14 C:\\KodeCreators\\newlife-panel\\app\\Http\\Controllers\\Admin\\ScriptController.php(1174): App\\Http\\Controllers\\Admin\\ScriptController->regeneratePdfWithSignature(Object(App\\Models\\ImportFile))
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\ScriptController->signAll(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('signAll', Array)
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\ScriptController), 'signAll')
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\ForcePasswordChange.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ForcePasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#68 {main}
"} 
[2025-06-02 12:23:44] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:53:43 +0630","user_id":3} 
[2025-06-02 12:24:31] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:54:30 +0630","user_id":3} 
[2025-06-02 12:24:50] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:54:49 +0630","user_id":3} 
[2025-06-02 12:24:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:54:55 +0630","user_id":3} 
[2025-06-02 12:24:59] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:54:59 +0630","user_id":3} 
[2025-06-02 12:25:01] local.ERROR: Failed to parse client timestamp {"timestamp":"2025-06-02 17:55:01 +0630","error":"Trailing data"} 
[2025-06-02 12:25:01] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-02 17:54:59 +0630"} 
[2025-06-02 12:25:01] local.INFO: Regenerating PDF with signed_at timestamp {"import_file_id":6,"signed_at_db":"2025-06-02 17:54:59","formatted_signed_at":"06/02/2025 05:54 PM"} 
[2025-06-02 12:25:02] local.INFO: ScriptStatusChanged event dispatched from signAll {"user_id":3,"user_name":"sahil panchal","count":1} 
[2025-06-02 12:25:05] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:55:04 +0630","user_id":3} 
[2025-06-02 12:25:09] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:55:08 +0630","user_id":3} 
[2025-06-02 12:25:30] local.INFO: Device time stored in session {"device_time":"2025-06-02 17:55:29 +0630","user_id":1} 
[2025-06-02 12:31:55] local.INFO: Device time stored in session {"device_time":"2025-06-02 18:01:55 +0630","user_id":3} 
[2025-06-02 12:32:05] local.INFO: Device time stored in session {"device_time":"2025-06-02 18:02:05 +0630","user_id":1} 
[2025-06-03 04:16:54] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:46:53 +0630","user_id":1} 
[2025-06-03 04:16:57] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:46:56 +0630","user_id":1} 
[2025-06-03 04:17:04] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:02 +0630","user_id":1} 
[2025-06-03 04:17:10] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:09 +0630","user_id":1} 
[2025-06-03 04:17:13] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:12 +0630","user_id":1} 
[2025-06-03 04:17:16] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:15 +0630","user_id":1} 
[2025-06-03 04:17:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:29 +0630","user_id":1} 
[2025-06-03 04:17:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:37 +0630","user_id":1} 
[2025-06-03 04:17:49] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:48 +0630","user_id":1} 
[2025-06-03 04:17:51] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:47:51 +0630","user_id":1} 
[2025-06-03 04:19:18] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:49:17 +0630","user_id":1} 
[2025-06-03 04:20:31] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:50:30 +0630","user_id":1} 
[2025-06-03 04:21:17] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:51:16 +0630","user_id":1} 
[2025-06-03 04:21:56] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:51:55 +0630","user_id":1} 
[2025-06-03 04:22:28] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 04:22:31] local.INFO: Staff bulk import completed {"import_id":2,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 04:22:34] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:52:33 +0630","user_id":1} 
[2025-06-03 04:23:11] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:53:11 +0630","user_id":3} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":6,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":7,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":8,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":9,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Processing row {"row_number":10,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 04:23:18] local.INFO: Error statistics {"total_rows":9,"error_rows":0,"valid_rows":9} 
[2025-06-03 04:23:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:53:20 +0630","user_id":3} 
[2025-06-03 04:23:22] local.INFO: Processing Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0} 
[2025-06-03 04:23:22] local.INFO: Excel processing completed {"processed_rows":9,"skipped_rows":0,"import_id":3} 
[2025-06-03 04:23:25] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:53:24 +0630","user_id":3} 
[2025-06-03 04:23:59] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:53:58 +0630","user_id":1} 
[2025-06-03 04:24:21] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:54:21 +0630","user_id":1} 
[2025-06-03 04:24:32] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:54:31 +0630","user_id":1} 
[2025-06-03 04:25:19] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:55:18 +0630","user_id":1} 
[2025-06-03 04:26:53] local.INFO: Device time stored in session {"device_time":"2025-06-03 09:56:52 +0630","user_id":1} 
[2025-06-03 04:31:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:01:48 +0630","user_id":1} 
[2025-06-03 04:31:59] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:01:59 +0630","user_id":1} 
[2025-06-03 04:33:32] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:03:31 +0630","user_id":1} 
[2025-06-03 04:33:42] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:03:41 +0630","user_id":1} 
[2025-06-03 04:33:51] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:03:50 +0630","user_id":1} 
[2025-06-03 04:33:57] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:03:56 +0630","user_id":1} 
[2025-06-03 04:34:05] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:04:04 +0630","user_id":1} 
[2025-06-03 04:34:07] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 04:34:08] local.INFO: Staff bulk import completed {"import_id":4,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 04:34:11] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:04:10 +0630","user_id":1} 
[2025-06-03 04:46:52] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:16:52 +0630","user_id":1} 
[2025-06-03 04:48:42] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:18:41 +0630","user_id":1} 
[2025-06-03 04:48:50] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:18:50 +0630","user_id":1} 
[2025-06-03 04:48:55] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:18:54 +0630","user_id":1} 
[2025-06-03 04:49:00] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:19:00 +0630","user_id":1} 
[2025-06-03 04:50:26] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:20:26 +0630","user_id":1} 
[2025-06-03 04:50:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:20:30 +0630","user_id":1} 
[2025-06-03 04:50:35] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:20:34 +0630","user_id":1} 
[2025-06-03 04:51:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:21:37 +0630","user_id":1} 
[2025-06-03 04:51:44] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:21:44 +0630","user_id":1} 
[2025-06-03 04:51:54] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:21:53 +0630","user_id":1} 
[2025-06-03 04:52:31] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:22:30 +0630","user_id":1} 
[2025-06-03 04:52:43] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:22:43 +0630","user_id":1} 
[2025-06-03 04:57:11] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:27:10 +0630","user_id":1} 
[2025-06-03 04:57:58] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:27:57 +0630","user_id":1} 
[2025-06-03 04:58:41] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:28:40 +0630","user_id":1} 
[2025-06-03 04:59:55] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:29:54 +0630","user_id":1} 
[2025-06-03 05:00:13] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:30:13 +0630","user_id":1} 
[2025-06-03 05:01:05] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:31:05 +0630","user_id":1} 
[2025-06-03 05:01:31] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:31:30 +0630","user_id":1} 
[2025-06-03 05:01:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:31:48 +0630","user_id":1} 
[2025-06-03 05:02:49] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:32:48 +0630","user_id":1} 
[2025-06-03 05:03:18] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:33:17 +0630","user_id":1} 
[2025-06-03 05:17:26] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:47:25 +0630","user_id":1} 
[2025-06-03 05:21:32] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:51:31 +0630","user_id":1} 
[2025-06-03 05:21:53] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:51:52 +0630","user_id":1} 
[2025-06-03 05:22:01] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:52:00 +0630","user_id":1} 
[2025-06-03 05:23:39] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:53:39 +0630","user_id":1} 
[2025-06-03 05:24:43] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:54:43 +0630","user_id":1} 
[2025-06-03 05:25:41] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:55:41 +0630","user_id":1} 
[2025-06-03 05:27:40] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:57:39 +0630","user_id":1} 
[2025-06-03 05:28:31] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:58:31 +0630","user_id":1} 
[2025-06-03 05:28:57] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:58:56 +0630","user_id":1} 
[2025-06-03 05:29:25] local.INFO: Device time stored in session {"device_time":"2025-06-03 10:59:25 +0630","user_id":1} 
[2025-06-03 05:30:08] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:00:07 +0630","user_id":1} 
[2025-06-03 05:30:13] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:00:13 +0630","user_id":1} 
[2025-06-03 05:30:31] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:00:31 +0630","user_id":4} 
[2025-06-03 05:30:42] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:00:41 +0630","user_id":1} 
[2025-06-03 05:30:50] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"4","selected_provider_name":"ashish parmar"} 
[2025-06-03 05:30:50] local.INFO: Staff bulk import completed {"import_id":5,"processed_rows":9,"skipped_rows":0,"provider_id":"4","provider_name":"aashish"} 
[2025-06-03 05:30:53] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:00:53 +0630","user_id":1} 
[2025-06-03 05:31:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:01:29 +0630","user_id":1} 
[2025-06-03 05:32:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:02:48 +0630","user_id":1} 
[2025-06-03 05:33:32] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:03:31 +0630","user_id":1} 
[2025-06-03 05:34:18] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:04:17 +0630","user_id":1} 
[2025-06-03 05:34:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:04:48 +0630","user_id":1} 
[2025-06-03 05:36:41] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:06:40 +0630","user_id":1} 
[2025-06-03 05:36:50] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:06:49 +0630","user_id":1} 
[2025-06-03 05:37:02] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:07:01 +0630","user_id":1} 
[2025-06-03 05:37:23] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:07:22 +0630","user_id":1} 
[2025-06-03 05:37:28] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:07:28 +0630","user_id":1} 
[2025-06-03 05:37:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:07:37 +0630","user_id":1} 
[2025-06-03 05:37:54] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:07:53 +0630","user_id":1} 
[2025-06-03 05:39:03] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:09:02 +0630","user_id":1} 
[2025-06-03 05:39:04] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"4","selected_provider_name":"ashish parmar"} 
[2025-06-03 05:39:05] local.INFO: Staff bulk import completed {"import_id":6,"processed_rows":9,"skipped_rows":0,"provider_id":"4","provider_name":"aashish"} 
[2025-06-03 05:39:08] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:09:07 +0630","user_id":1} 
[2025-06-03 05:41:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:11:19 +0630","user_id":1} 
[2025-06-03 05:41:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:11:37 +0630","user_id":4} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":6,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":7,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":8,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":9,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Processing row {"row_number":10,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 05:41:42] local.INFO: Error statistics {"total_rows":9,"error_rows":0,"valid_rows":9} 
[2025-06-03 05:41:44] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:11:44 +0630","user_id":4} 
[2025-06-03 05:41:45] local.INFO: Processing Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0} 
[2025-06-03 05:41:46] local.INFO: Excel processing completed {"processed_rows":9,"skipped_rows":0,"import_id":7} 
[2025-06-03 05:41:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:11:47 +0630","user_id":4} 
[2025-06-03 05:41:51] local.INFO: Update Status Request {"import_id":"7","ids":null,"status":"Pending Approval"} 
[2025-06-03 05:41:51] local.INFO: Processing all records for import {"import_id":"7"} 
[2025-06-03 05:41:51] local.INFO: Filtering for New status only  
[2025-06-03 05:41:51] local.INFO: Update Status Query {"sql":"select * from `import_files` where `import_id` = ? and `status` = ?","bindings":["7","New"]} 
[2025-06-03 05:41:51] local.INFO: Records that will be updated {"record_ids":[55,56,57,58,59,60,61,62,63],"count":9} 
[2025-06-03 05:41:51] local.INFO: User information for signature {"user_id":4,"user_name":"ashish parmar","has_signature":"Yes","signature_path":"signatures/Ebd3ZRXMbDDoXBUZQPcA3R534HIXpmajMtc3CeZ1.png"} 
[2025-06-03 05:41:51] local.INFO: Signature image loaded successfully {"user_id":4,"signature_path":"signatures/Ebd3ZRXMbDDoXBUZQPcA3R534HIXpmajMtc3CeZ1.png","mime_type":"image/png"} 
[2025-06-03 05:41:51] local.INFO: Using device time from session for signed_at {"timestamp":"2025-06-03 11:11:47 +0630"} 
[2025-06-03 05:41:51] local.INFO: Starting PDF regeneration for 9 files  
[2025-06-03 05:41:52] local.INFO: Processed 5 of 9 PDFs  
[2025-06-03 05:41:53] local.INFO: Completed PDF regeneration. Processed 9 of 9 files  
[2025-06-03 05:41:54] local.INFO: ScriptStatusChanged event dispatched {"user_id":4,"user_name":"ashish parmar","count":9} 
[2025-06-03 05:41:57] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:11:56 +0630","user_id":4} 
[2025-06-03 05:44:37] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:14:36 +0630","user_id":1} 
[2025-06-03 05:45:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:15:37 +0630","user_id":1} 
[2025-06-03 05:47:08] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:17:07 +0630","user_id":1} 
[2025-06-03 05:48:35] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:18:34 +0630","user_id":1} 
[2025-06-03 05:49:45] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:19:45 +0630","user_id":1} 
[2025-06-03 05:50:07] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:20:07 +0630","user_id":1} 
[2025-06-03 05:50:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:20:29 +0630","user_id":1} 
[2025-06-03 05:50:42] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:20:41 +0630","user_id":1} 
[2025-06-03 05:55:22] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:25:21 +0630","user_id":1} 
[2025-06-03 05:55:36] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:25:35 +0630","user_id":1} 
[2025-06-03 05:57:04] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:27:03 +0630","user_id":1} 
[2025-06-03 05:57:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:27:19 +0630","user_id":1} 
[2025-06-03 06:05:15] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:35:14 +0630","user_id":1} 
[2025-06-03 06:05:28] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:35:28 +0630","user_id":1} 
[2025-06-03 06:06:50] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:36:50 +0630","user_id":1} 
[2025-06-03 06:07:22] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:37:21 +0630","user_id":1} 
[2025-06-03 06:09:43] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:39:42 +0630","user_id":1} 
[2025-06-03 06:10:07] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:40:06 +0630","user_id":1} 
[2025-06-03 06:10:12] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:40:11 +0630","user_id":1} 
[2025-06-03 06:10:29] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:40:28 +0630","user_id":1} 
[2025-06-03 06:16:13] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:46:12 +0630","user_id":1} 
[2025-06-03 06:16:22] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:46:21 +0630","user_id":1} 
[2025-06-03 06:16:34] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"4","selected_provider_name":"ashish parmar"} 
[2025-06-03 06:16:34] local.INFO: Staff bulk import completed {"import_id":8,"processed_rows":9,"skipped_rows":0,"provider_id":"4","provider_name":"aashish"} 
[2025-06-03 06:16:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:46:37 +0630","user_id":1} 
[2025-06-03 06:16:50] local.INFO: Download All PDF Request {"displayed_ids":["64","65","66","67","68","69","70","71","72"],"all_with_status":null,"import_id":"8","status":"New","route":"excel.download-all-pdf","request_method":"POST","all_request_params":{"_token":"1oqD7zE5N5PUR9Lh1EbyftaQ0fObjzqjLjCfnGi1","status":"created","displayed_ids":["64","65","66","67","68","69","70","71","72"]}} 
[2025-06-03 06:16:50] local.INFO: Initial query before filtering by IDs {"sql":"select * from `import_files` where `import_id` = ? and (`status` = ? or `status` is null)","bindings":[8,"New"]} 
[2025-06-03 06:16:50] local.INFO: Record count before ID filtering {"count":9} 
[2025-06-03 06:16:50] local.INFO: Filtering by displayed IDs {"count":9,"ids":[64,65,66,67,68,69,70,71,72]} 
[2025-06-03 06:16:50] local.INFO: Query after filtering by IDs {"sql":"select * from `import_files` where `import_id` = ? and (`status` = ? or `status` is null) and `id` in (?, ?, ?, ?, ?, ?, ?, ?, ?)","bindings":[8,"New",64,65,66,67,68,69,70,71,72]} 
[2025-06-03 06:16:50] local.INFO: Files that will be included in the ZIP {"count":9,"file_ids":[72,71,70,69,68,67,66,65,64],"file_names":["prescription_9_Lorraine_Geissler.pdf","prescription_8_Martin_Schoendienst.pdf","prescription_7_Teri_Degiacomo.pdf","prescription_6_Douglas_Forsman.pdf","prescription_5_Laura_Reynolds.pdf","prescription_4_Susan_Johnson.pdf","prescription_3_Brian_House.pdf","prescription_2_Brenda_Kirkpatrick.pdf","prescription_1_Lucia_Castillo_Dorantes.pdf"]} 
[2025-06-03 06:21:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:51:30 +0630","user_id":1} 
[2025-06-03 06:21:46] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:51:45 +0630","user_id":1} 
[2025-06-03 06:22:24] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:52:24 +0630","user_id":1} 
[2025-06-03 06:23:22] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:53:22 +0630","user_id":1} 
[2025-06-03 06:24:33] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:54:32 +0630","user_id":1} 
[2025-06-03 06:25:26] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:55:25 +0630","user_id":1} 
[2025-06-03 06:25:35] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:55:34 +0630","user_id":1} 
[2025-06-03 06:26:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:56:19 +0630","user_id":1} 
[2025-06-03 06:26:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:56:47 +0630","user_id":1} 
[2025-06-03 06:27:10] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:57:09 +0630","user_id":1} 
[2025-06-03 06:27:24] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:57:24 +0630","user_id":1} 
[2025-06-03 06:28:02] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:58:01 +0630","user_id":1} 
[2025-06-03 06:29:03] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:59:03 +0630","user_id":1} 
[2025-06-03 06:29:11] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:59:11 +0630","user_id":1} 
[2025-06-03 06:29:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:59:19 +0630","user_id":1} 
[2025-06-03 06:29:48] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:59:47 +0630","user_id":1} 
[2025-06-03 06:29:56] local.INFO: Device time stored in session {"device_time":"2025-06-03 11:59:55 +0630","user_id":1} 
[2025-06-03 06:30:07] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:00:06 +0630","user_id":1} 
[2025-06-03 06:30:18] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:00:18 +0630","user_id":1} 
[2025-06-03 06:30:53] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:00:52 +0630","user_id":1} 
[2025-06-03 06:31:05] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:01:05 +0630","user_id":1} 
[2025-06-03 06:31:23] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:01:22 +0630","user_id":1} 
[2025-06-03 06:32:26] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:02:25 +0630","user_id":1} 
[2025-06-03 06:36:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:06:29 +0630","user_id":1} 
[2025-06-03 06:38:10] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:08:09 +0630","user_id":1} 
[2025-06-03 06:38:18] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:08:17 +0630","user_id":1} 
[2025-06-03 06:38:36] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:08:35 +0630","user_id":1} 
[2025-06-03 06:39:14] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:09:13 +0630","user_id":1} 
[2025-06-03 06:39:24] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:09:23 +0630","user_id":1} 
[2025-06-03 06:39:36] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:09:35 +0630","user_id":1} 
[2025-06-03 06:39:38] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[],"error_rows_count":0,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 06:39:39] local.INFO: Staff bulk import completed {"import_id":9,"processed_rows":9,"skipped_rows":0,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 06:39:42] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:09:41 +0630","user_id":1} 
[2025-06-03 06:44:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:14:30 +0630","user_id":1} 
[2025-06-03 06:44:36] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:14:35 +0630","user_id":1} 
[2025-06-03 06:44:50] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:14:49 +0630","user_id":1} 
[2025-06-03 06:44:54] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:14:54 +0630","user_id":1} 
[2025-06-03 06:49:17] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:19:17 +0630","user_id":1} 
[2025-06-03 06:49:27] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:19:27 +0630","user_id":1} 
[2025-06-03 06:51:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:21:19 +0630","user_id":1} 
[2025-06-03 06:53:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:23:20 +0630","user_id":1} 
[2025-06-03 06:53:30] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:23:29 +0630","user_id":1} 
[2025-06-03 07:02:19] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:32:19 +0630","user_id":1} 
[2025-06-03 07:02:28] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:32:28 +0630","user_id":1} 
[2025-06-03 07:04:14] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:34:14 +0630","user_id":1} 
[2025-06-03 07:04:24] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:34:23 +0630","user_id":1} 
[2025-06-03 07:04:28] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:34:27 +0630","user_id":1} 
[2025-06-03 07:04:47] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:34:46 +0630","user_id":1} 
[2025-06-03 07:05:11] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[5,6,7],"error_rows_count":3,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 07:05:11] local.INFO: Skipping row with errors {"excel_row_number":5,"index":3} 
[2025-06-03 07:05:11] local.INFO: Skipping row with errors {"excel_row_number":6,"index":4} 
[2025-06-03 07:05:11] local.INFO: Skipping row with errors {"excel_row_number":7,"index":5} 
[2025-06-03 07:05:11] local.INFO: Staff bulk import completed {"import_id":10,"processed_rows":6,"skipped_rows":3,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 07:05:14] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:35:14 +0630","user_id":1} 
[2025-06-03 07:05:26] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:35:25 +0630","user_id":1} 
[2025-06-03 07:05:38] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:35:38 +0630","user_id":1} 
[2025-06-03 07:09:06] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:39:05 +0630","user_id":4} 
[2025-06-03 07:09:16] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:39:15 +0630","user_id":1} 
[2025-06-03 07:09:32] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:39:32 +0630","user_id":1} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":6,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":7,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":8,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":9,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Processing row {"row_number":10,"column_count":17,"has_data":true,"highest_column_index":17,"consecutive_blank_rows":0} 
[2025-06-03 07:10:18] local.INFO: Error statistics {"total_rows":9,"error_rows":3,"valid_rows":6} 
[2025-06-03 07:10:20] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:40:20 +0630","user_id":4} 
[2025-06-03 07:10:50] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[5,6,7],"error_rows_count":3,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 07:10:51] local.INFO: Skipping row with errors {"excel_row_number":5,"index":3} 
[2025-06-03 07:10:51] local.INFO: Skipping row with errors {"excel_row_number":6,"index":4} 
[2025-06-03 07:10:51] local.INFO: Skipping row with errors {"excel_row_number":7,"index":5} 
[2025-06-03 07:10:51] local.INFO: Staff bulk import completed {"import_id":11,"processed_rows":6,"skipped_rows":3,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 07:10:53] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:40:53 +0630","user_id":1} 
[2025-06-03 07:10:59] local.INFO: Processing Excel data {"total_rows":9,"error_rows":[5,6,7],"error_rows_count":3} 
[2025-06-03 07:10:59] local.INFO: Filtering out error rows before processing  
[2025-06-03 07:10:59] local.INFO: Skipping row with errors {"excel_row_number":5,"index":3} 
[2025-06-03 07:10:59] local.INFO: Skipping row with errors {"excel_row_number":6,"index":4} 
[2025-06-03 07:10:59] local.INFO: Skipping row with errors {"excel_row_number":7,"index":5} 
[2025-06-03 07:10:59] local.INFO: Excel processing completed {"processed_rows":6,"skipped_rows":3,"import_id":12} 
[2025-06-03 07:11:02] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:41:01 +0630","user_id":4} 
[2025-06-03 07:17:02] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:47:02 +0630","user_id":4} 
[2025-06-03 07:17:10] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:47:09 +0630","user_id":1} 
[2025-06-03 07:17:22] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:47:21 +0630","user_id":1} 
[2025-06-03 07:17:24] local.INFO: Processing Staff Excel data {"total_rows":9,"error_rows":[5,6,7],"error_rows_count":3,"selected_provider_id":"3","selected_provider_name":"sahil panchal"} 
[2025-06-03 07:17:24] local.INFO: Skipping row with errors {"excel_row_number":5,"index":3} 
[2025-06-03 07:17:24] local.INFO: Skipping row with errors {"excel_row_number":6,"index":4} 
[2025-06-03 07:17:24] local.INFO: Skipping row with errors {"excel_row_number":7,"index":5} 
[2025-06-03 07:17:24] local.INFO: Staff bulk import completed {"import_id":13,"processed_rows":6,"skipped_rows":3,"provider_id":"3","provider_name":"sahil"} 
[2025-06-03 07:17:27] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:47:26 +0630","user_id":1} 
[2025-06-03 07:17:55] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:47:55 +0630","user_id":1} 
[2025-06-03 07:18:05] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:48:04 +0630","user_id":1} 
[2025-06-03 07:18:23] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:48:23 +0630","user_id":1} 
[2025-06-03 07:18:29] local.INFO: Blank row detected {"row_number":2,"consecutive_blank_rows":1} 
[2025-06-03 07:18:29] local.INFO: Processing row {"row_number":2,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":1} 
[2025-06-03 07:18:29] local.INFO: Blank row detected {"row_number":3,"consecutive_blank_rows":2} 
[2025-06-03 07:18:29] local.INFO: Processing row {"row_number":3,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":2} 
[2025-06-03 07:18:29] local.INFO: Blank row detected {"row_number":4,"consecutive_blank_rows":3} 
[2025-06-03 07:18:29] local.INFO: Processing row {"row_number":4,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":3} 
[2025-06-03 07:18:29] local.INFO: Blank row detected {"row_number":5,"consecutive_blank_rows":4} 
[2025-06-03 07:18:29] local.INFO: Processing row {"row_number":5,"column_count":17,"has_data":false,"highest_column_index":17,"consecutive_blank_rows":4} 
[2025-06-03 07:18:29] local.INFO: Blank row detected {"row_number":6,"consecutive_blank_rows":5} 
[2025-06-03 07:18:29] local.INFO: 5 or more consecutive blank rows detected - stopping processing and discarding blank rows {"row_number":6,"consecutive_blank_rows":5} 
[2025-06-03 07:18:32] local.INFO: Device time stored in session {"device_time":"2025-06-03 12:48:31 +0630","user_id":4} 
